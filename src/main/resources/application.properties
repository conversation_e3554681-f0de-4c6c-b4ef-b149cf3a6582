server.port=8000
server.tomcat.threads.max=500
server.tomcat.threads.min-spare=20
server.tomcat.max-connections=10000
server.tomcat.accept-count=600
server.tomcat.max-http-form-post-size=10MB
server.tomcat.max-swallow-size=10MB
server.shutdown=graceful

logging.file.name=/home/<USER>/logs/cash-business/cash-business.log
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level][%X{traceId:-},%X{spanId:-}][%thread] %logger{50}.%method:%line - %msg%n
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level][%X{traceId:-},%X{spanId:-}][%thread] %logger{50}.%method:%line - %msg%n
logging.level.com.netflix.discovery=warn
logging.logback.rollingpolicy.max-file-size=500MB
logging.logback.rollingpolicy.total-size-cap=1500MB

spring.servlet.multipart.max-file-size=10MB

spring.application.name=cash-flow
spring.config.import=apollo://
spring.rabbitmq.listener.simple.acknowledge-mode=manual
spring.rabbitmq.listener.direct.acknowledge-mode=manual
spring.jpa.open-in-view=false

spring.data.redis.repositories.enabled=false

spring.cloud.openfeign.compression.response.enabled=true
spring.cloud.loadbalancer.health-check.refetch-instances-interval=10s
spring.cloud.loadbalancer.cache.ttl = 12s

eureka.instance.prefer-ip-address=true
eureka.instance.lease-expiration-duration-in-seconds=12
eureka.instance.lease-renewal-interval-in-seconds=4
eureka.client.registry-fetch-interval-seconds=5
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
spring.jpa.properties.hibernate.auto_quote_keyword=true

management.endpoints.web.exposure.include=health,serviceregistry

spring.freemarker.prefix=classpath:/templates/
spring.freemarker.suffix=.html
spring.freemarker.settings.default_encoding=utf-8
spring.cloud.sentinel.enabled=true
spring.cloud.sentinel.eager=true
spring.cloud.sentinel.transport.dashboard=${SENTINEL_DASHBOARD_URL:http://localhost:18080}
spring.cloud.sentinel.transport.client-ip=${SENTINEL_CLIENT_IP:}
spring.cloud.sentinel.transport.port=${SENTINEL_TRANSPORT_PORT:8719}

# RabbitMQ
spring.rabbitmq.addresses=localhost:5672
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest
